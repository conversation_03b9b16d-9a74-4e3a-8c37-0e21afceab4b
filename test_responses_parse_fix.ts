#!/usr/bin/env tsx

/**
 * Test script to verify that client.responses.parse works with wrapOpenAI in TypeScript
 */

import { OpenAI } from "openai";
import { wrap<PERSON>pen<PERSON><PERSON>, initLogger, traced } from "braintrust";
import { z } from "zod";

// Initialize Braintrust
initLogger({ projectName: "test-responses-parse-ts" });

// Wrap the OpenAI client
const client = wrapOpenAI(new OpenAI());

// Define a Zod schema for structured output
const CalendarEventSchema = z.object({
  name: z.string(),
  date: z.string(),
  participants: z.array(z.string()),
});

type CalendarEvent = z.infer<typeof CalendarEventSchema>;

const testResponsesParse = traced(
  async function testResponsesParse(): Promise<CalendarEvent | { error: string }> {
    try {
      const response = await client.responses.parse({
        model: "gpt-4o-mini",
        input: [
          { role: "system", content: "Extract the event information from the user's message." },
          { role: "user", content: "<PERSON> and <PERSON> are going to a science fair on Friday." },
        ],
        text_format: CalendarEventSchema,
      });

      // Access the parsed output
      const event = response.output_parsed;
      console.log(`Event name: ${event.name}`);
      console.log(`Event date: ${event.date}`);
      console.log(`Participants: ${event.participants.join(", ")}`);

      return event;
    } catch (error) {
      console.error(`Error: ${error}`);
      return { error: String(error) };
    }
  },
  { notrace_io: true }
);

async function main() {
  console.log("Testing client.responses.parse with wrapOpenAI (TypeScript)...");
  
  const result = await testResponsesParse();
  console.log(`Result:`, result);
  
  console.log("Test completed. Check Braintrust logs to see if tracing worked.");
}

main().catch(console.error);
