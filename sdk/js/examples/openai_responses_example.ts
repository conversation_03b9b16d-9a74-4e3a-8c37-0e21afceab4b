#!/usr/bin/env tsx

import OpenAI from "openai";
import { wrap<PERSON>pen<PERSON><PERSON>, initLogger } from "braintrust";
import { z } from "zod";

initLogger({ projectName: "typescript-examples" });

const client = wrapOpenAI(new OpenAI());

// Define a schema for structured output
const CalendarEventSchema = z.object({
  name: z.string(),
  date: z.string(),
  participants: z.array(z.string()),
});

async function main() {
  console.log("Call responses.create");
  const response = await client.responses.create({
    model: "gpt-4o-mini",
    instructions: "It is the year 2000",
    input: "What is the best book?",
  });

  console.log(response.output_text);

  console.log("Call responses.parse");
  const parseResponse = await client.responses.parse({
    model: "gpt-4o-mini",
    input: [
      { role: "system", content: "Extract event information from the user's message." },
      { role: "user", content: "<PERSON> and <PERSON> are going to a science fair on Friday." },
    ],
    text_format: CalendarEventSchema,
  });

  console.log("Parsed event:", parseResponse.output_parsed);

  console.log("Call responses.stream");
  const stream = await client.responses.create({
    model: "gpt-4o-mini",
    input: "What are 5 books I should read?",
    stream: true,
  });

  let count = 0;
  for await (const event of stream) {
    count++;
  }
  console.log("streamed", count, "events");
}

main().catch(console.error);
