# Change terraform.tfvars to update these
variable "pulse_version" {
  description = "Docker image version for the pulse service"
  type        = string
}

variable "otel_collector_version" {
  description = "Docker image version for the OpenTelemetry collector sidecar"
  type        = string
}

variable "force_new_deployment" {
  description = "Force a new deployment of the service. Useful when using static version like 'latest'."
  type        = bool
  default     = false
}
