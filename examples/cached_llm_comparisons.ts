import { <PERSON><PERSON><PERSON>ein } from "autoevals";
import { Eval, wrap<PERSON><PERSON><PERSON><PERSON> } from "braintrust";
import OpenAI from "openai";

const client = wrapOpenAI(
  new OpenAI({
    // have the local proxy return whatever hard coded cached response
    baseURL: "http://localhost:8000/v1/proxy",
    apiKey: process.env.OPENAI_API_KEY, // Can use Braintrust, Anthropic, etc. API keys here
  }),
);

const data = [
  {
    input: "tomato",
    expected: "tomato",
    metadata: {
      cache: false,
    },
  },
  {
    input: "BANANA",
    expected: "banana",
    metadata: {
      cache: true,
    },
  },
  {
    input: "carrot",
    expected: "carrot",
    metadata: {
      cache: true,
    },
  },
  {
    input: "Apple",
    expected: "apple",
    metadata: {
      cache: true,
    },
  },
  {
    input: "orange",
    expected: "orange",
    metadata: {
      cache: true,
    },
  },
];

async function task(
  input: string,
  { metadata }: { metadata: { cache: boolean } },
) {
  const ret = await client.chat.completions.create(
    {
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `What is this? Respond with one word only.`,
        },
        {
          role: "user",
          content: input,
        },
      ],
      seed: 1,
    },
    {
      headers: {
        ...(!metadata.cache ? { "cache-control": "no-cache" } : {}),
      },
    },
  );
  return ret.choices[0].message.content ?? "";
}

const experimentName = "cached llm comparisons";
await Eval(experimentName, {
  data: () =>
    data.map(({ input, expected, metadata }) => ({
      input,
      expected,
      metadata,
    })),
  task,
  scores: [Levenshtein],
});

await Eval(experimentName, {
  data: () =>
    data.map(({ input, expected }) => ({
      input,
      expected,
      metadata: {
        // cache everything
        cache: true,
      },
    })),
  task,
  scores: [Levenshtein],
});
