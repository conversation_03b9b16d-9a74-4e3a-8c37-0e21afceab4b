import type { AggregationType } from "#/utils/queries/aggregations";
import { Combobox } from "#/ui/combobox/combobox";
import { useDuckDB } from "#/utils/duckdb";
import { metadataFieldsQuery } from "#/utils/queries/metadata";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { FoldVertical, StretchHorizontal, UnfoldVertical } from "lucide-react";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useState,
} from "react";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { CheckItem } from "#/ui/combobox/combobox-command";
import { cn } from "#/utils/classnames";
import { type ViewProps } from "#/utils/view/use-view";

export const GROUP_BY_INPUT_VALUE = "__bt_input";

export function useTableGroupingControl({
  baseQuery,
  signals,
  tableIdentifier,
  customFieldNames,
  viewProps,
}: {
  baseQuery: string | null;
  signals: number[];
  tableIdentifier: string;
  customFieldNames?: string[];
  viewProps: ViewProps;
}) {
  const [groupAggregationTypes, setGroupAggregationTypes] = useState<
    Record<string, AggregationType>
  >({});
  const setGroupAggregationType = useCallback(
    (name: string, value: AggregationType) => {
      setGroupAggregationTypes((prev) => ({
        ...prev,
        [name]: value,
      }));
    },
    [setGroupAggregationTypes],
  );

  const duck = useDuckDB();
  const metadataQuery = useQuery({
    queryKey: ["metadataFields", tableIdentifier, baseQuery],
    queryFn: async ({ signal }: { signal: AbortSignal }) => {
      const conn = await duck!.connect();
      return metadataFieldsQuery({
        conn,
        abortSignal: signal,
        parse: true,
        query: baseQuery,
      });
    },
    staleTime: Infinity,
    enabled: !!duck && !!baseQuery && signals.every((s) => s > 0),
  });

  const signalsSum = signals.reduce((acc, s) => acc + s, 0);
  const queryClient = useQueryClient();
  useEffect(() => {
    queryClient.invalidateQueries({
      queryKey: ["metadataFields", tableIdentifier],
    });
  }, [queryClient, signalsSum, tableIdentifier]);

  const options =
    metadataQuery.data?.map((f) => ({
      label: f.join("."),
      value: JSON.stringify(f),
    })) ?? [];

  const customColumnOptions =
    customFieldNames?.map((f) => ({
      label: f,
      value: f,
    })) ?? [];

  const { grouping, setGrouping } = viewProps;
  const tableGrouping =
    [...options, ...customColumnOptions].find((o) => o.value === grouping)
      ?.value ?? GROUP_BY_NONE_VALUE;

  const selectComponent = (
    <GroupBySelect
      options={options}
      tableGrouping={tableGrouping}
      onTableGrouping={setGrouping}
      customColumnOptions={customColumnOptions}
    />
  );

  return {
    tableGrouping,
    groupAggregationTypes,
    setGroupAggregationType,
    setTableGrouping: setGrouping,
    selectComponent,
  };
}

export const GroupBySelect = ({
  options,
  allowGroupByInput,
  tableGrouping,
  onTableGrouping,
  groupRowData,
  expandData,
  customColumnOptions,
  disabled,
}: {
  options: { label: string; value: string }[];
  allowGroupByInput?: boolean;
  tableGrouping: string;
  onTableGrouping: (value: string) => void;
  groupRowData?: {
    showGroupRows: boolean;
    onShowGroupRows: Dispatch<SetStateAction<boolean>>;
  };
  expandData?: {
    expandAllGroups: () => void;
    collapseAllGroups: () => void;
    areAllRowsExpanded: boolean;
    areAllRowsCollapsed: boolean;
  };
  customColumnOptions?: { label: string; value: string }[];
  disabled?: boolean;
}) => {
  const hasOptions =
    options.length > 0 ||
    (customColumnOptions && customColumnOptions.length > 0);
  return (
    <Combobox
      disabled={disabled}
      tooltipContent="Group"
      options={
        allowGroupByInput
          ? [
              {
                label: "Group by",
                options: [
                  { label: "None", value: GROUP_BY_NONE_VALUE },
                  { label: "Input", value: GROUP_BY_INPUT_VALUE },
                ],
              },
              ...(options.length > 0
                ? [
                    {
                      label: "Metadata",
                      options,
                    },
                  ]
                : []),
              ...(customColumnOptions && customColumnOptions.length > 0
                ? [
                    {
                      label: "Other",
                      options: customColumnOptions,
                    },
                  ]
                : []),
            ]
          : [
              hasOptions
                ? {
                    label: "None",
                    value: GROUP_BY_NONE_VALUE,
                  }
                : {
                    label:
                      "To group rows add row metadata, a custom column, or run with trials to group by input",
                    disabled: true,
                    value: "",
                  },
              ...options,
              ...(customColumnOptions || []),
            ]
      }
      bottomActions={[
        ...(groupRowData
          ? [
              {
                label: (
                  <div className="flex items-center">
                    <CheckItem isSelected={groupRowData.showGroupRows}>
                      Include comparisons in groups
                    </CheckItem>
                  </div>
                ),
                onSelect: () => {
                  groupRowData.onShowGroupRows((v) => !v);
                },
                disabled: !hasOptions,
              },
            ]
          : []),
        ...(expandData
          ? [
              {
                label: (
                  <div className="flex items-center">
                    <UnfoldVertical className={cn("mr-2 size-3")} />
                    Expand all groups
                  </div>
                ),
                disabled:
                  tableGrouping === GROUP_BY_NONE_VALUE ||
                  expandData.areAllRowsExpanded,
                onSelect: () => {
                  expandData.expandAllGroups();
                },
              },
              {
                label: (
                  <div className="flex items-center">
                    <FoldVertical className={cn("mr-2 size-3")} />
                    Collapse all groups
                  </div>
                ),
                disabled:
                  tableGrouping === GROUP_BY_NONE_VALUE ||
                  expandData.areAllRowsCollapsed,
                onSelect: () => {
                  expandData.collapseAllGroups();
                },
              },
            ]
          : []),
      ]}
      align="start"
      onChange={(v) => {
        onTableGrouping(v ?? GROUP_BY_NONE_VALUE);
      }}
      renderComboboxDisplayLabel={() => {
        return <ButtonLabel />;
      }}
      // in the case where there is a metadata seleciton but no matching option
      placeholderLabel={<ButtonLabel />}
      iconClassName="hidden"
      variant="button"
      buttonSize="xs"
      buttonVariant={
        tableGrouping === GROUP_BY_NONE_VALUE ? "ghost" : "default"
      }
      selectedValue={tableGrouping}
      searchPlaceholder="Find grouping"
      noResultsLabel="No metadata"
    />
  );
};

const ButtonLabel = () => {
  return (
    <span className="flex items-center gap-1">
      <StretchHorizontal className="size-3" />
      <span className="hidden @lg/controls:block">Group</span>
    </span>
  );
};
