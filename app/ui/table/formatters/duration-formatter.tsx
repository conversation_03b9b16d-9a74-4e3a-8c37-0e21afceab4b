import {
  Diff<PERSON><PERSON><PERSON><PERSON><PERSON>,
  DiffRightField,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { type FormatterProps } from "#/ui/arrow-table";
import { DiffNumbers } from "#/ui/diff";
import { isEmpty } from "#/utils/object";
import { NullFormatter } from "./null-formatter";
import {
  GroupAggregationFormatterFactory,
  type GroupAggregationProps,
} from "./group-aggregation-formatter";
import { CachedIndicator, hasCachedMetric } from "./cached-indicator";

export const formatDuration = (duration: number) =>
  `${Math.round(duration * 10) / 10}s`;

export function DurationFormatter<TsData, TsValue>({
  value,
  meta,
  diffIndex,
  showCached,
  ...props
}: FormatterProps<TsData, TsValue> & { showCached?: boolean }) {
  if (isDiffObject<number>(value)) {
    const left = value[DiffLeftField];
    const right = value[DiffRightField];
    return (
      <DiffNumbers
        oldNumber={left ?? null}
        newNumber={right ?? null}
        formatNumber={formatDuration}
        percentDiff={(start, end) => (end - start) / start}
        formatDiff={(start, end) => `${Math.abs(end - start).toFixed(1)}s`}
        upIsGood={false}
        {...props}
      />
    );
  }
  const renderValue =
    value && Number.isFinite(value) ? (
      formatDuration(value)
    ) : isEmpty(value) ? (
      <NullFormatter />
    ) : (
      value
    );

  return (
    <>
      {renderValue}
      {showCached &&
        props.renderForTooltip &&
        hasCachedMetric(props.row.original, diffIndex) && <CachedIndicator />}
    </>
  );
}

export const DurationFormatterMetadata = {
  parseValue: (v: unknown) => formatDuration(Number(v)),
};

export const DurationWithAggregationFormatter = ({
  isGrouping,
  ...groupAggregationProps
}: GroupAggregationProps) => ({
  cell: GroupAggregationFormatterFactory({
    render: (props) => <DurationFormatter {...props} showCached />,
    renderForGroup: (props) => formatDuration(props.value),
    ...groupAggregationProps,
  }),
  colSize:
    groupAggregationProps.summaryEnabled || isGrouping
      ? {
          minSize: 200,
          size: 200,
        }
      : undefined,
});
