import {
  createDiffObjectSchema,
  getDiffValue,
} from "#/utils/diffs/diff-objects";
import { z } from "zod";

const cachedMetricSchema = z.object({
  metrics: z.object({
    cached: z.union([z.number().nullish(), createDiffObjectSchema(z.number())]),
  }),
});

export function hasCachedMetric(rowOriginal: unknown, diffIndex?: number) {
  const parsed = cachedMetricSchema.safeParse(rowOriginal);
  if (!parsed.success) {
    return false;
  }
  return (
    getDiffValue({
      value: parsed.data.metrics.cached,
      index: diffIndex ?? 0,
    }) === 1
  );
}

export function CachedIndicator({}) {
  return (
    <div className="mt-1 text-primary-500">
      LLM calls were cached by Braintrust proxy
    </div>
  );
}
