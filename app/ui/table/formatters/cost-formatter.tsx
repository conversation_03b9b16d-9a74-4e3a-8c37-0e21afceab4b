import { type FormatterProps } from "#/ui/arrow-table";
import { DiffNumbers } from "#/ui/diff";
import { formatPriceValue } from "#/ui/type-formatters/metrics";
import {
  DiffLeftField,
  DiffRightField,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { z } from "zod";
import { NullFormatter } from "./null-formatter";
import {
  GroupAggregationFormatterFactory,
  type GroupAggregationProps,
} from "./group-aggregation-formatter";
import { CachedIndicator, hasCachedMetric } from "./cached-indicator";

export function CostFormatter<TsTable, TsValue>({
  value,
  meta,
  showCached,
  diffIndex,
  ...props
}: FormatterProps<TsTable, TsValue> & { showCached?: boolean }) {
  if (isDiffObject<number>(value)) {
    const left = value[DiffLeftField];
    const right = value[DiffRightField];
    return (
      <DiffNumbers
        oldNumber={left ?? null}
        newNumber={right ?? null}
        formatNumber={formatPriceValue}
        percentDiff={(start, end) => (end - start) / start}
        formatDiff={(start, end) =>
          formatPriceValue(
            Math.abs(Number(end.toFixed(3)) - Number(start.toFixed(3))),
          )
        }
        upIsGood={false}
        {...props}
      />
    );
  }

  // While shifting diff mode in output_vs_expected, we might flow a plain object value (eg from)
  // the metadata field through this formatter until all of the effects run. So we need to confirm
  // that the value is a number before formatting it.
  const numericValue = z.number().safeParse(value);
  const cachedContent =
    showCached && hasCachedMetric(props.row.original, diffIndex) ? (
      <CachedIndicator />
    ) : null;
  const renderValue = numericValue.success ? (
    formatPriceValue(cachedContent ? 0 : numericValue.data)
  ) : (
    <NullFormatter />
  );
  return (
    <>
      {renderValue}
      {props.renderForTooltip && cachedContent}
    </>
  );
}

export const CostFormatterMetadata = {
  parseValue: (v: unknown) => formatPriceValue(Number(v)),
};

export const CostWithAggregationFormatter = ({
  isGrouping,
  ...groupAggregationProps
}: GroupAggregationProps) => ({
  cell: GroupAggregationFormatterFactory({
    render: (props) => <CostFormatter {...props} showCached />,
    renderForGroup: (props) => formatPriceValue(props.value),
    ...groupAggregationProps,
  }),
  colSize:
    groupAggregationProps.summaryEnabled || isGrouping
      ? {
          minSize: 200,
          size: 200,
        }
      : undefined,
});
