import { getModelIcon } from "#/app/app/[org]/prompt/[prompt]/model-icon";
import { useOrg } from "#/utils/user";
import { useAvailableModels } from "#/ui/prompts/models";

export const useModelConfig = (model?: string) => {
  const { name: orgName } = useOrg();
  const { allAvailableModels } = useAvailableModels({ orgName });

  if (!model || typeof model !== "string") return null;

  const ModelIcon = getModelIcon(model);

  const modelDisplayName = allAvailableModels[model]?.displayName || model;

  return { ModelIcon, modelDisplayName };
};
