import { type LogTimeBucket } from "#/app/app/[org]/p/[project]/(ExperimentsChart)/logs-progress-insight";
import { type RenderOption } from "#/utils/parse";
import {
  type ColumnOrderState,
  type ColumnSizingState,
  type VisibilityState,
} from "@tanstack/react-table";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import type { APIVersionInfo } from "#/ui/api-version/check-api-version";
import { type SelectionType } from "#/ui/charts/selectionTypes";
import { MINIMUM_BTQL_API_VERSION } from "#/utils/btql/constants";
import type {
  AggregationFieldType,
  AggregationType,
} from "#/utils/queries/aggregations";
import { type TableRowHeight } from "#/ui/table-row-height-toggle";
import { type UrlSearch } from "#/utils/search/search";
import { type TableLayoutType } from "#/ui/table/layout-type-control";
import { type ChatSessions } from "#/ui/optimization/use-global-chat-context";
import { type Annotations } from "#/ui/charts/annotations";
import { type MonitorViewOptions } from "@braintrust/core/typespecs";
import { type TimeRangeFilter } from "#/utils/view/use-view";
import { type SyncedPlaygroundBlock } from "#/ui/prompts/schema";
import { type FeatureFlags } from "./feature-flag-config";

type ExperimentChartType = "ScoreDistribution" | "MetadataGroupedChart";
export type ChartAggregationType = AggregationType | "all";

export const MIN_CHART_HEIGHT = 100;

const getEntityKey = (
  entityType: string,
  entityIdentifier: string,
  key: string,
) => {
  const entityTypeEncoded = encodeURIComponent(entityType);
  const entityIdentifierEncoded = encodeURIComponent(entityIdentifier);
  const entityEncoded = encodeURIComponent(key);
  return [entityTypeEncoded, entityIdentifierEncoded, entityEncoded].join(":");
};

export type EntityStorageSchema = {
  app: {
    sidePanelWidth: string;
    parseMode: RenderOption | null;
    featureFlags: Partial<FeatureFlags>;
    headerMenuProjectsSort: "alphabetical" | "createdAt";
    isSidenavCollapsed: boolean | undefined;
  };
  org: {
    webappVersion: string;
    apiVersion3: APIVersionInfo;
    codeExecutionWarmed: boolean;
    copilotModel: string | undefined;
    disableDefaultComparisonAutoSelect: boolean;
    recentProjectIds: string[];
  };
  project: {
    chartConfiguration: {
      // These charts are for experiment but we store them in project level
      chartsOrder: ExperimentChartType[];
    };
    logBucket: LogTimeBucket;
    humanReviewAutoAdvance: boolean;
  };
  functions: {
    runEditorValue: Record<string, unknown>;
  };
  playground: {
    maxConcurrency: number;
    loggedOutPrompts: SyncedPlaygroundBlock[];
  };
  tables: {
    search: UrlSearch;
    columnWidthConfiguration: ColumnSizingState;
    columnVisibility: VisibilityState;
    columnOrderConfiguration: ColumnOrderState;
    recentFilters: {
      btql: string;
      label: string;
      type?: string;
      comparison?: { experimentId: string };
    }[];
    grouping: string | null;
    rowHeight: TableRowHeight | null;
    tallGroupRows: boolean;
    layout: TableLayoutType | null;
    aggregationTypes: Record<AggregationFieldType, AggregationType>;
    defaultView: string | null;
    disableInfiniteScroll: boolean;
    timeRangeFilter: TimeRangeFilter | undefined;
  };
  charts: {
    // deprecated
    columnVisibility: Record<string, boolean>;
    // deprecated
    excludedScores: string[] | null;
    excludedMeasures: SelectionType[] | null;
    yMetric: SelectionType | null;
    xAxis: SelectionType | null;
    symbolGrouping: SelectionType | null;
    xAxisAggregation: ChartAggregationType | null;
    annotations: Annotations;
    groupBy: string;
    groupByMetadataField: string;
    groupByMetric: string;
    height: number;
  };
  monitorChart: {
    rowType: MonitorViewOptions["type"];
    groupBy: string;
    projectId: string;
    tzUTC?: boolean;
  };
  summaryBreakdown: {
    invisibleScores: string[];
    metricAggregationType: AggregationType;
  };
  docs: {
    snippetTabIndex: number;
  };
  prompt: {
    evaluatorName: string | null;
    evaluatorCode: Record<string, string>;
  };
  collapsibleSection: {
    isCollapsed: boolean;
  };
  editorMode: {
    mode: RenderOption | undefined;
  };
  traceTree: {
    isCollapsed: boolean | null;
  };
  dataEditor: {
    foldState: Record<string, boolean>;
  };
  dismissableMessages: {
    promptVariableTipDismissed: boolean;
  };
  optimization: {
    isDocked: boolean;
    isChatOpen: boolean;
    chatSessions: ChatSessions;
    currentChatSessionId: string;
    model: string;
    allowRunningWithoutConsent: boolean;
  };
  btql: {
    editorValue: string;
  };
};
export type Entity = keyof EntityStorageSchema;

export const getDefaultEntityStorageValues = (): EntityStorageSchema => ({
  app: {
    sidePanelWidth: "50vw",
    parseMode: null,
    featureFlags: {},
    headerMenuProjectsSort: "alphabetical",
    isSidenavCollapsed: undefined,
  },
  org: {
    webappVersion: "",
    apiVersion3: {
      version: MINIMUM_BTQL_API_VERSION,
      universal: false,
      code_execution: false,
      brainstore_default: false,
      brainstore_can_contain_row_refs: false,
      has_logs2: false,
    },
    disableDefaultComparisonAutoSelect: false,
    codeExecutionWarmed: false,
    copilotModel: undefined,
    recentProjectIds: [],
  },
  project: {
    chartConfiguration: {
      chartsOrder: ["ScoreDistribution", "MetadataGroupedChart"],
    },
    logBucket: "hour",
    humanReviewAutoAdvance: false,
  },
  functions: {
    runEditorValue: {},
  },
  playground: {
    maxConcurrency: 10,
    loggedOutPrompts: [],
  },
  tables: {
    search: {},
    columnWidthConfiguration: {},
    columnVisibility: {},
    columnOrderConfiguration: [],
    recentFilters: [],
    grouping: null,
    rowHeight: null,
    tallGroupRows: true,
    layout: null,
    aggregationTypes: {
      scores: "avg",
      metrics: "sum",
    },
    defaultView: null,
    disableInfiniteScroll: false,
    timeRangeFilter: undefined,
  },
  charts: {
    columnVisibility: {},
    excludedScores: null,
    excludedMeasures: null,
    yMetric: null,
    xAxis: null,
    xAxisAggregation: null,
    symbolGrouping: null,
    annotations: [],
    groupBy: "",
    groupByMetadataField: "",
    groupByMetric: "",
    height: MIN_CHART_HEIGHT,
  },
  monitorChart: {
    rowType: "project",
    groupBy: "",
    projectId: "",
  },
  summaryBreakdown: {
    invisibleScores: [],
    metricAggregationType: "avg",
  },
  docs: {
    snippetTabIndex: 0,
  },
  prompt: {
    evaluatorName: null,
    evaluatorCode: {},
  },
  collapsibleSection: {
    isCollapsed: false,
  },
  editorMode: {
    mode: undefined,
  },
  traceTree: {
    isCollapsed: null,
  },
  dataEditor: {
    foldState: {},
  },
  dismissableMessages: {
    promptVariableTipDismissed: false,
  },
  optimization: {
    isDocked: false,
    isChatOpen: false,
    chatSessions: {
      sessions: [],
    },
    currentChatSessionId: "",
    model: "",
    allowRunningWithoutConsent: false,
  },
  btql: {
    editorValue: "",
  },
});

function getDefaultValue<
  E extends Entity,
  K extends keyof EntityStorageSchema[E],
>(entityType: E, key: K, override?: EntityStorageSchema[E][K]) {
  if (override !== undefined) {
    return override;
  }

  return getDefaultEntityStorageValues()[entityType][key];
}

interface Storage {
  get: <T>(_key: string) => T | null;
  // to help with memoizing the parsed result
  getRaw: (_key: string) => string | null;
  set: <T>(_key: string, _value: T) => void;
}
// Start with localStorage, can be extended to use other storage types
const storage: Storage = {
  get: <T>(key: string) => {
    let value;
    try {
      value = localStorage.getItem(key);
    } catch (e) {
      console.warn(`Failed to get item from localStorage for key: ${key}`, e);
      return null;
    }
    if (value) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return JSON.parse(value) as T;
    }
    return null;
  },
  getRaw: (key: string) => {
    try {
      return localStorage.getItem(key);
    } catch (e) {
      console.warn(`Failed to get item from localStorage for key: ${key}`, e);
      return null;
    }
  },
  set: <T>(key: string, value: T) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (e) {
      console.warn(`Failed to set item in localStorage for key: ${key}`, e);
    }
  },
};

export const useEntityStorageAccess = <
  E extends Entity,
  K extends keyof EntityStorageSchema[E],
>({
  entityType,
  entityIdentifier,
  defaultValueOverride,
  persistDefaultValueOnMiss = true,
}: {
  entityType: E;
  entityIdentifier: string;
  defaultValueOverride?: EntityStorageSchema[E][K];
  persistDefaultValueOnMiss?: boolean;
}) => {
  const getRaw = useCallback(
    (key: K) => {
      if (typeof window === "undefined") {
        return null;
      }

      const storageKey = getEntityKey(
        entityType,
        entityIdentifier,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        key as string,
      );
      return storage.getRaw(storageKey);
    },
    [entityType, entityIdentifier],
  );

  const get = useCallback(
    (key: K) => {
      const defaultValue = getDefaultValue(
        entityType,
        key,
        defaultValueOverride,
      );
      if (typeof window === "undefined") {
        return defaultValue;
      }

      const storageKey = getEntityKey(
        entityType,
        entityIdentifier,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        key as string,
      );

      try {
        const existingValue =
          storage.get<EntityStorageSchema[E][K]>(storageKey);
        if (existingValue !== null && existingValue !== undefined) {
          return existingValue;
        }
      } catch (e) {}
      if (persistDefaultValueOnMiss) storage.set(storageKey, defaultValue);
      return defaultValue;
    },
    [
      entityType,
      defaultValueOverride,
      entityIdentifier,
      persistDefaultValueOnMiss,
    ],
  );

  const set = useCallback(
    (key: K, value: EntityStorageSchema[E][K]) => {
      const storageKey = getEntityKey(
        entityType,
        entityIdentifier,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        key as string,
      );
      storage.set(storageKey, value);
    },
    [entityType, entityIdentifier],
  );

  return {
    get,
    getRaw,
    set,
  };
};

export type Value<
  E extends Entity,
  K extends keyof EntityStorageSchema[E],
> = EntityStorageSchema[E][K];
export type SetValue<T> = Dispatch<SetStateAction<T>>;
/**
 * @example
 * const [projectChartConf, setProjectChartConf] = useEntityStorage("project", projectId, "chartConfiguration");
 */
export const useEntityStorage = <
  E extends Entity,
  K extends keyof EntityStorageSchema[E],
>({
  entityType,
  entityIdentifier,
  key,
  defaultValue,
  onMount,
  persistDefaultValueOnMiss,
}: {
  entityType: E;
  entityIdentifier: string;
  key: K;
  defaultValue?: Value<E, K>;
  /** if true, will set the value to the default value on mount to avoid hydration errors */
  onMount?: boolean;
  persistDefaultValueOnMiss?: boolean;
}): [Value<E, K>, SetValue<Value<E, K>>, () => void] => {
  const { get, getRaw, set } = useEntityStorageAccess({
    entityType,
    entityIdentifier,
    defaultValueOverride: defaultValue,
    persistDefaultValueOnMiss,
  });
  const rawValue = getRaw(key);
  // we cannot return the result from get(key) directly if it is an object
  // otherwise it will fail dependency equivalence checks
  const value = useMemo(
    () => get(key),
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [get, key, rawValue],
  );

  const [_, setUpdated] = useState(0);
  useEffect(() => {
    const handleStorageUpdate = (e: StorageEvent) => {
      const entityKey = getEntityKey(
        entityType,
        entityIdentifier,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        key as string,
      );
      if (e.key === entityKey) {
        setUpdated((prev) => prev + 1);
      }
    };
    window.addEventListener("storage", handleStorageUpdate);
    return function cleanup() {
      window.removeEventListener("storage", handleStorageUpdate);
    };
  }, [entityType, entityIdentifier, key, setUpdated]);

  const setStoredValue = useCallback<SetValue<Value<E, K>>>(
    (newValue) => {
      const oldValue = get(key);
      const updatedValue =
        newValue instanceof Function ? newValue(oldValue) : newValue;
      if (oldValue === updatedValue) return;

      set(key, updatedValue);

      const entityKey = getEntityKey(
        entityType,
        entityIdentifier,
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        key as string,
      );
      // use storage events in the case where we multiple callsites with the same entityKey
      // https://stackoverflow.com/a/65348883
      window.dispatchEvent(new StorageEvent("storage", { key: entityKey }));
    },
    [get, set, entityType, entityIdentifier, key],
  );

  const resetValue = useCallback(() => {
    const v = getDefaultValue(entityType, key, defaultValue);
    setStoredValue(v);
  }, [entityType, key, defaultValue, setStoredValue]);

  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return [
    onMount && defaultValue !== undefined
      ? isMounted
        ? value
        : defaultValue
      : value,
    setStoredValue,
    resetValue,
  ];
};
