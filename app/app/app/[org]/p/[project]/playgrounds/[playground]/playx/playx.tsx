import { newId } from "#/utils/btapi/btapi";
import {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect,
  type Dispatch,
  type SetStateAction,
} from "react";
import {
  type EvalRequest,
  type CreateExperimentTask,
} from "#/app/app/[org]/prompt/[prompt]/create-experiment-dialog";
import { savedScorerToFunctionId, type SavedScorer } from "#/utils/scorers";
import {
  type StreamingMode,
  type RunEvalRequest,
  type objectReferenceSchema,
  type RemoteEvalData,
} from "@braintrust/core/typespecs";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { runEval, stopEval } from "./stream";
import { useSetAtom, useAtom } from "jotai";
import { streamingCompletionsAtom, stopRequestedAtom } from "./atoms";
import { type PromptSessionData } from "../use-prompt-session-data";
import { useJSONMemo } from "#/utils/memo";
import { useFeatureFlags } from "#/lib/feature-flags";
import useEvent from "react-use-event-hook";
import { singleQuote } from "#/utils/sql-utils";
import { type z } from "zod";
import isEqual from "lodash.isequal";
import {
  resetRunningStateAtom,
  latestXactIdAtom,
  runningInfoAtom,
  streamingStateAtom,
} from "./atoms";
import { type UIFunction } from "#/ui/prompts/schema";
import {
  InsertRecordsResult,
  type TransactionIdField,
  type UpdateLog,
} from "#/utils/duckdb";
import { deserializeJSONStringAsString } from "#/utils/object";
import { type Search } from "#/utils/search/search";

export type PlaygroundRecord = {
  id: string;
  [TransactionIdField]: string;
  input: unknown;
  expected: unknown;
  metadata: unknown;
};

export type DatasetRowParam = {
  rowId: string;
  updatePlaygroundRows?: {
    inputValue?: Partial<PlaygroundRecord>;
    ids: (string | null)[];
    playgroundLogXactId: string;
    created: string;
    _xactId: string;
  };
};

export type PlayXRunPromptsArgs = {
  rowIdx?: number; // playx uses the id rather than the ordinal, so we can remove this once we've migrated
  colIdx?: number;
  datasetRow?: DatasetRowParam;
};
export type PlayXRunPromptsFn = (args: PlayXRunPromptsArgs) => Promise<void>;

export function usePlayxState({
  promptSessionId,
  promptToGeneration,
  setPromptSessionData,
  getFunctions,
  datasetId,
  savedScorers,
  scorerFunctions,
  setTopLevelError,
  setIsRunning,
  streamingMode, // TODO: implement this
  maxConcurrency,
  numDatasetRecords,
  strict,
  extraMessages,
  channels,
  setSearch,
}: {
  promptSessionId: string | null;
  promptToGeneration: Record<string, string> | undefined;
  setPromptSessionData: (promptSessionData: Partial<PromptSessionData>) => void;
  getFunctions: (args: {
    useInlinePrompts: boolean;
  }) => Promise<(CreateExperimentTask & { id: string })[]>;
  datasetId: string | null;
  savedScorers: SavedScorer[];
  scorerFunctions: Record<string, UIFunction>;

  // Playground state
  setIsRunning: (isRunning: boolean) => void;
  setTopLevelError: (error: string | null) => void;

  streamingMode?: StreamingMode;
  maxConcurrency?: number;
  numDatasetRecords: number;
  strict: boolean | undefined;
  extraMessages: string | undefined;
  channels: UpdateLog[];
  setSearch: Dispatch<SetStateAction<Search>>;
}) {
  const org = useOrg();
  const proxyUrl = org.proxy_url;
  const { flags } = useFeatureFlags();
  const { getOrRefreshToken } = useSessionToken();

  const setStreamingCompletions = useSetAtom(streamingCompletionsAtom);
  const setStreamingState = useSetAtom(streamingStateAtom);
  const setLatestXactId = useSetAtom(latestXactIdAtom);
  const abortControllerRef = useRef<AbortController>(null);
  const [stopRequested, setStopRequested] = useAtom(stopRequestedAtom);
  const [stopToken, setStopToken] = useState<string | null>(null);
  const setRunningInfo = useSetAtom(runningInfoAtom);
  const streamingStateTimeout = useRef<number | null>(null);

  const runPrompts = useEvent(
    async ({ colIdx, datasetRow }: PlayXRunPromptsArgs) => {
      setTopLevelError(null);

      if (!promptSessionId) {
        throw new Error("No prompt session id");
      }

      if (streamingStateTimeout.current) {
        clearTimeout(streamingStateTimeout.current);
      }

      // Initialize the prompt to generation map.
      const newPromptToGeneration: Record<string, string> = {
        ...(promptToGeneration ?? {}),
      };

      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const stopToken = `stop_function:${promptSessionId}:${newId()}`;
      setStopToken(stopToken);
      setIsRunning(true);
      // Use inline prompts to avoid race condition when running playground after editing prompts
      const tasks = (await getFunctions({ useInlinePrompts: true })).filter(
        (task, i) => colIdx === undefined || colIdx === i,
      );

      const evalSpecs: {
        evalData: RunEvalRequest["data"];
        parentOrigin?: z.infer<typeof objectReferenceSchema> | undefined;
      }[] = tasks.map((_, i) => {
        if (!datasetId) {
          return {
            evalData: {
              // to help scorers
              data: [{ expected: null }],
            },
          };
        }

        const upsertId = datasetRow?.updatePlaygroundRows?.ids[i];
        if (datasetRow?.updatePlaygroundRows && upsertId) {
          return {
            parentOrigin: {
              object_type: "dataset" as const,
              object_id: datasetId,
              id: datasetRow.rowId,
              _xact_id: datasetRow.updatePlaygroundRows._xactId,
              created: new Date(
                datasetRow.updatePlaygroundRows.created,
              ).toISOString(),
            },
            evalData: {
              data: [
                {
                  expected: null,
                  ...datasetRow.updatePlaygroundRows.inputValue,
                  upsert_id: upsertId,
                },
              ],
            },
          };
        }

        return {
          evalData: {
            dataset_id: datasetId,
            _internal_btql: {
              ...(datasetRow
                ? {
                    filter: {
                      btql: `id = ${singleQuote(datasetRow.rowId)}`,
                    },
                  }
                : {}),
              // run in dataset order so streaming looks nicer
              sort: [
                {
                  expr: {
                    op: "ident",
                    name: ["created"],
                  },
                  dir: "desc",
                },
                {
                  expr: {
                    op: "ident",
                    name: ["id"],
                  },
                  dir: "desc",
                },
              ],
            },
          },
        };
      });

      const generations: string[] = [];
      const evalRequests: EvalRequest[] = [];
      for (let i = 0; i < tasks.length; i++) {
        const { id, ...task } = tasks[i];

        // preserve existing generations, update new ones
        const generation =
          (datasetRow ? promptToGeneration?.[id] : null) ?? newId();
        generations.push(generation);
        newPromptToGeneration[id] = generation;

        const parent: RunEvalRequest["parent"] = {
          object_type: "playground_logs",
          object_id: promptSessionId,
          propagated_event: {
            origin: evalSpecs[i].parentOrigin,
            span_attributes: {
              generation,
            },
          },
        };

        if (
          "inline_function" in task &&
          task.inline_function.type === "remote_eval"
        ) {
          evalRequests.push({
            type: "remote",
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            request: task.inline_function as RemoteEvalData,
            data: evalSpecs[i].evalData,
            scores: savedScorers.flatMap((s) =>
              s.type === "global" || scorerFunctions[s.id]
                ? [
                    {
                      function_id: savedScorerToFunctionId(s),
                      name:
                        s.type === "global"
                          ? s.name
                          : (scorerFunctions[s.id].name ?? `scorer_${s.id}`),
                    },
                  ]
                : [],
            ),
            parent,
            stream: true,
          });
        } else {
          evalRequests.push({
            type: "api",
            request: {
              experiment_name: task.name,
              metadata: task.metadata,
              project_id: "",
              data: evalSpecs[i].evalData,
              task,
              scores: savedScorers.flatMap((s) =>
                s.type === "global" || scorerFunctions[s.id]
                  ? [savedScorerToFunctionId(s)]
                  : [],
              ),
              stream: true,
              trial_count: 1,
              max_concurrency: maxConcurrency ?? null,
              parent,
              strict: strict ?? undefined,
              ...(flags.playXStop ? { stop_token: stopToken } : {}),
              extra_messages: extraMessages,
            },
          });
        }
      }

      if (!isEqual(promptToGeneration, newPromptToGeneration)) {
        // When re-running prompts, if there are comparison filters, changing the generation
        // will mean the filters no longer apply properly, so update them
        // e.g running a column
        const generationToPrompt = Object.fromEntries(
          Object.entries(promptToGeneration ?? {}).map(([k, v]) => [v, k]),
        );
        setSearch((prev) =>
          Object.fromEntries(
            Object.entries(prev ?? {}).map(([k, v]) => [
              k,
              v?.flatMap((c) => {
                if ("comparison" in c && c.comparison) {
                  const nextGenerationId =
                    // in the un-run case, the experiment id is the prompt id
                    newPromptToGeneration[c.comparison.experimentId] ??
                    newPromptToGeneration[
                      generationToPrompt[c.comparison.experimentId]
                    ];
                  if (
                    // if there are no matching prompts in the new generation map,
                    // that could be the case of:
                    // 1. the column hasn't been run yet for this playground
                    // 2. the user clicked "run column" for a different column
                    !nextGenerationId ||
                    nextGenerationId === c.comparison.experimentId
                  ) {
                    return [c];
                  }
                  if (c.type === "sort") {
                    return [
                      {
                        ...c,
                        comparison: {
                          ...c.comparison,
                          experimentId: nextGenerationId,
                        },
                      },
                    ];
                  }

                  return [
                    {
                      ...c,
                      comparison: {
                        experimentId: nextGenerationId,
                      },
                    },
                  ];
                }

                return [c];
              }),
            ]),
          ),
        );
        setPromptSessionData({
          generations: newPromptToGeneration,
        });
      }
      setStreamingCompletions({});
      setStreamingState(undefined);
      setRunningInfo({
        generationIds: generations,
        rowIds: datasetRow ? [datasetRow.rowId] : undefined,
        totalDatasetRows: numDatasetRecords,
      });
      channels.forEach((c) => {
        c.insertRecordsError = InsertRecordsResult.SUCCESS;
        c.hasRejoined = false;
      });
      if (datasetRow?.updatePlaygroundRows) {
        setLatestXactId(datasetRow.updatePlaygroundRows.playgroundLogXactId);
      }

      const sessionToken = await getOrRefreshToken();

      try {
        await Promise.all(
          evalRequests.map((req, idx) =>
            runEval({
              proxyUrl,
              runEvalRequest: req,
              sessionToken,
              orgName: org.name,
              onError: (e) => {
                setTopLevelError(deserializeJSONStringAsString(e));
              },
              setCompletion: (datasetRecordId, callback) => {
                setStreamingCompletions((prev) => {
                  const generationId = generations[idx];
                  const next = {
                    ...prev,
                  };

                  const existing = next[datasetRecordId]?.[generationId];
                  const newCompletion = callback(existing ?? {});
                  if (existing === newCompletion || !newCompletion) {
                    return next;
                  }
                  next[datasetRecordId] = {
                    ...next[datasetRecordId],
                    [generationId]: newCompletion,
                  };
                  return next;
                });
              },
              signal: abortController.signal,
            }),
          ),
        );
      } catch (e) {
        if (e instanceof DOMException && e.name === "AbortError") {
          // aborted during streaming
          return;
        }
        console.error(e);
      } finally {
        setIsRunning(false);
        // since there can be a delay between the finish of this fetch call and
        // the first loaded playground log, we can set this hacky delay.
        // This should go away once we change realtime with the summary data
        streamingStateTimeout.current = window.setTimeout(() => {
          streamingStateTimeout.current = null;
          setStreamingState((prev) =>
            prev === "streaming" ? "eval-finished" : prev,
          );
        }, 1000 * 10);
      }
    },
  );

  const stop = useCallback(() => {
    setStopRequested(true);
  }, [setStopRequested]);

  const resetRunningState = useSetAtom(resetRunningStateAtom);
  useEffect(() => {
    const _inner = async () => {
      if (stopRequested) {
        if (abortControllerRef.current) {
          if (!abortControllerRef.current.signal.aborted) {
            abortControllerRef.current.abort();
          }
          abortControllerRef.current = null;
        }

        if (flags.playXStop && stopToken) {
          const sessionToken = await getOrRefreshToken();
          await stopEval({
            proxyUrl,
            stopToken,
            sessionToken,
            orgName: org.name,
          });
        }

        setStopToken(null);
        setStopRequested(false);
        resetRunningState();
      }
    };
    _inner();
  }, [
    stopRequested,
    stopToken,
    flags.playXStop,
    getOrRefreshToken,
    proxyUrl,
    org.name,
    setStopToken,
    setStopRequested,
    setStreamingCompletions,
    setIsRunning,
    resetRunningState,
  ]);

  const generationIdToPromptId = useJSONMemo(
    useMemo(() => {
      return promptToGeneration && Object.keys(promptToGeneration).length > 0
        ? Object.fromEntries(
            Object.entries(promptToGeneration)
              .filter(([_, g]) => g !== null)
              .map(([p, g]) => [g, p]),
          )
        : {};
    }, [promptToGeneration]),
  );

  return {
    runPrompts,
    stop,
    generationIdToPromptId,
  };
}
