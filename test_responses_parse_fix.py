#!/usr/bin/env python3

"""
Test script to verify that client.responses.parse works with wrap_openai
"""

import braintrust
from openai import OpenAI
from pydantic import BaseModel
from typing import List

# Initialize Braintrust
logger = braintrust.init_logger(project="test-responses-parse")

# Wrap the OpenAI client
client = braintrust.wrap_openai(OpenAI())

# Define a Pydantic model for structured output
class CalendarEvent(BaseModel):
    name: str
    date: str
    participants: List[str]

@braintrust.traced(notrace_io=True)
def test_responses_parse():
    """Test function using client.responses.parse"""
    try:
        response = client.responses.parse(
            model="gpt-4o-mini",
            input=[
                {"role": "system", "content": "Extract the event information from the user's message."},
                {"role": "user", "content": "<PERSON> and <PERSON> are going to a science fair on Friday."},
            ],
            text_format=CalendarEvent,
        )
        
        # Access the parsed output
        event = response.output_parsed
        print(f"Event name: {event.name}")
        print(f"Event date: {event.date}")
        print(f"Participants: {event.participants}")
        
        return {
            "name": event.name,
            "date": event.date,
            "participants": event.participants
        }
        
    except Exception as e:
        print(f"Error: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    print("Testing client.responses.parse with wrap_openai...")
    
    with logger:
        result = test_responses_parse()
        print(f"Result: {result}")
    
    print("Test completed. Check Braintrust logs to see if tracing worked.")
