import asyncio

import braintrust
from openai import AsyncOpenAI, OpenAI, pydantic_function_tool
from pydantic import BaseModel

logger = braintrust.init_logger("openai responses")

ARGS = dict(
    model="gpt-4o",
    input=[{"role": "system", "content": "say 10"}],
    text={
        "format": {
            "type": "json_schema",
            "name": "calendar_event",
            "schema": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "date": {"type": "string"},
                    "participants": {"type": "array", "items": {"type": "string"}},
                },
                "required": ["name", "date", "participants"],
                "additionalProperties": False,
            },
            "strict": True,
        }
    },
)


class QueryCalendarEvent(BaseModel):
    name: str
    date: str


class CalendarEvent(BaseModel):
    name: str
    date: str
    participants: list[str]


TOOLS = [QueryCalendarEvent]


def test_sync():
    client = braintrust.wrap_openai(OpenAI())
    print(client.responses.create(**ARGS))

    # Test client.responses.parse method
    parse_res = client.responses.parse(
        model="gpt-4o",
        input=[{"role": "system", "content": "Extract event info: Alice and Bob are going to a science fair on Friday."}],
        text_format=CalendarEvent,
    )
    print("responses.parse result:", parse_res)

    res = client.beta.chat.completions.parse(
        model="gpt-4o",
        messages=[{"role": "system", "content": "say 10"}],
        response_format=CalendarEvent,
        tools=[pydantic_function_tool(tool) for tool in TOOLS],
        temperature=0.0,
    )
    print(res)


async def test_async():
    async_client = braintrust.wrap_openai(AsyncOpenAI())
    print(await async_client.responses.create(**ARGS))

    # Test async client.responses.parse method
    async_parse_res = await async_client.responses.parse(
        model="gpt-4o",
        input=[{"role": "system", "content": "Extract event info: Alice and Bob are going to a science fair on Friday."}],
        text_format=CalendarEvent,
    )
    print("async responses.parse result:", async_parse_res)


test_sync()
asyncio.run(test_async())
braintrust.flush()
