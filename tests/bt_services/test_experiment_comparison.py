import difflib
import json
import time

import braintrust

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase
from tests.bt_services.experiment_test_base import ExperimentTestBase


class ExperimentComparisonTest(ExperimentTestBase):
    def get_experiment_comparison(self, experiment_id, base_experiment_id):
        return self.run_request(
            "get",
            f"{LOCAL_API_URL}/experiment-comparison2",
            params=dict(experiment_id=experiment_id, base_experiment_id=base_experiment_id),
        ).json()

    def test_basic(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")
        e1.log(input="a", output="out", scores=dict(score0=0.5, score1=0.8, score2=0.2))
        e1.log(input="b", output="out", scores=dict(score0=0.1, score1=0.5, score2=0.9))
        e1.log(input="c", output="out", scores=dict(score0=0.7, score1=0.3, score2=0.5))
        e2.log(input="b", output="out", scores=dict(score0=0.2, score2=0.5))
        e2.log(input="c", output="out", scores=dict(score0=0.9, score2=0.1))
        e2.log(input="d", output="out", scores=dict(score0=0.5, score2=0.7))
        e1.summarize()
        e2.summarize()

        self.verify_brainstore_summary(e1)
        self.verify_brainstore_summary(e2)

        comparison = self.get_experiment_comparison(e1.id, e2.id)["scores"]

        self.assertEqual(set(comparison.keys()), {"score0", "score1", "score2"})
        results = comparison["score0"]
        self.assertEqual(results["name"], "score0")
        self.assertAlmostEqual(results["score"], 1.3 / 3)
        self.assertAlmostEqual(results["diff"], -0.3 / 2)
        self.assertEqual(results["improvements"], 0)
        self.assertEqual(results["regressions"], 2)
        results = comparison["score2"]
        self.assertEqual(results["name"], "score2")
        self.assertAlmostEqual(results["score"], 1.6 / 3)
        self.assertAlmostEqual(results["diff"], 0.8 / 2)
        self.assertEqual(results["improvements"], 2)
        self.assertEqual(results["regressions"], 0)
        results = comparison["score1"]
        self.assertEqual(results["name"], "score1")
        self.assertIsNone(results.get("diff"))
        self.assertEqual(results["improvements"], 0)
        self.assertEqual(results["regressions"], 0)

    def test_compare_to_empty(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")
        e1.log(input="a", output="out", scores=dict(score0=0.5))
        e1.log(input="b", output="out", scores=dict(score0=0.1))
        e1.log(input="c", output="out", scores=dict(score0=0.7))
        e1.summarize()

        comparison = self.get_experiment_comparison(e1.id, e2.id)
        self.assertTrue("scores" in comparison)
        self.assertTrue("score0" in comparison["scores"])
        self.assertTrue("metrics" in comparison)
        self.assertTrue("duration" in comparison["metrics"])

        self.verify_brainstore_summary(e1)
        self.verify_brainstore_summary(e2)

    def test_compare_to_no_scores(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")
        e1.log(input="a", output="out", scores=dict(score0=0.5))
        e1.log(input="b", output="out", scores=dict(score0=0.1))
        e1.log(input="c", output="out", scores=dict(score0=0.7))
        e1.summarize()
        e2.log(input="a", output="out", scores=dict())
        e2.log(input="b", output="out", scores=dict())
        e2.log(input="c", output="out", scores=dict())
        e2.summarize()

        comparison = self.get_experiment_comparison(e1.id, e2.id)
        self.assertTrue("scores" in comparison)
        self.assertTrue("score0" in comparison["scores"])
        self.assertTrue("metrics" in comparison)
        self.assertTrue("duration" in comparison["metrics"])

        self.verify_brainstore_summary(e1)
        self.verify_brainstore_summary(e2)

    def test_compare_span_nesting(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")

        with e1.start_span() as root_span:
            root_span.log(input="a", scores=dict(score0=0.5), metrics=dict(start=0))
            with root_span.start_span("child") as child_span:
                child_span.log(input="b", scores=dict(score1=0.5), metrics=dict(start=0.1, end=0.2))
            with root_span.start_span("child2") as child_span:
                child_span.log(input="c", scores=dict(score2=0.5), metrics=dict(start=0.2, end=0.3))
            root_span.log(metrics=dict(end=0.3))
        with e2.start_span() as root_span:
            root_span.log(input="a", scores=dict(score0=0.2), metrics=dict(start=0.3))
            with root_span.start_span("child") as child_span:
                child_span.log(input="b", scores=dict(score1=0.7), metrics=dict(start=0.5, end=0.7))
            with root_span.start_span("child2") as child_span:
                child_span.log(input="d", scores=dict(score2=0.9), metrics=dict(start=0.7, end=0.9))
            root_span.log(metrics=dict(end=0.9))
        e1.summarize()
        e2.summarize()

        self.verify_brainstore_summary(e1)
        self.verify_brainstore_summary(e2)

        comparison = self.get_experiment_comparison(e1.id, e2.id)
        scores = comparison["scores"]
        results = scores["score0"]
        self.assertEqual(results["name"], "score0")
        self.assertAlmostEqual(results["score"], 0.5)
        self.assertAlmostEqual(results["diff"], 0.3)
        self.assertEqual(results["improvements"], 1)
        self.assertEqual(results["regressions"], 0)
        results = scores["score1"]
        self.assertEqual(results["name"], "score1")
        self.assertAlmostEqual(results["score"], 0.5)
        self.assertAlmostEqual(results["diff"], -0.2)
        self.assertEqual(results["improvements"], 0)
        self.assertEqual(results["regressions"], 1)
        results = scores["score2"]
        self.assertEqual(results["name"], "score2")
        self.assertAlmostEqual(results["score"], 0.5)
        self.assertAlmostEqual(results["diff"], -0.4)
        self.assertEqual(results["improvements"], 0)
        self.assertEqual(results["regressions"], 1)
        metrics = comparison["metrics"]
        duration = metrics["duration"]
        self.assertEqual(duration["name"], "duration")
        self.assertEqual(duration["unit"], "s")
        self.assertAlmostEqual(duration["metric"], 0.3)
        self.assertAlmostEqual(duration["diff"], -0.3)
        self.assertEqual(duration["improvements"], 1)
        self.assertEqual(duration["regressions"], 0)

    def test_compare_duplicate_input_ordering(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")
        e1.log(input="a", output="out", scores=dict(score0=0.1))
        e1.log(input="b", output="out", scores=dict(score0=0.2))
        e1.log(input="a", output="out", scores=dict(score0=0.5))
        e1.log(input="b", output="out", scores=dict(score0=0.6))
        e1.summarize()
        e2.log(input="a", output="out", scores=dict(score0=0.2))
        e2.log(input="b", output="out", scores=dict(score0=0.4))
        e2.log(input="a", output="out", scores=dict(score0=0.4))
        e2.log(input="b", output="out", scores=dict(score0=0.4))
        e2.summarize()

        self.verify_brainstore_summary(e1)
        self.verify_brainstore_summary(e2)

        # The query should average the scores across inputs, so the scores should be the same
        scores = self.get_experiment_comparison(e1.id, e2.id)["scores"]["score0"]
        self.assertEqual(scores["name"], "score0")
        self.assertAlmostEqual(scores["score"], 0.35)
        self.assertAlmostEqual(scores["diff"], 0.0)
        self.assertEqual(scores["improvements"], 0)
        self.assertEqual(scores["regressions"], 0)

    def test_trial_non_equal_averages(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")
        e1.log(input="a", output="out", scores=dict(score0=0.7))
        e1.log(input="b", output="out", scores=dict(score0=0.2))
        e1.log(input="a", output="out", scores=dict(score0=0.5))
        e1.log(input="b", output="out", scores=dict(score0=0.6))
        e1.summarize()
        e2.log(input="a", output="out", scores=dict(score0=0.2))
        e2.log(input="b", output="out", scores=dict(score0=0.4))
        e2.log(input="a", output="out", scores=dict(score0=0.4))
        e2.log(input="b", output="out", scores=dict(score0=0.4))

        # Even though these scores are really high, they should be ignored
        e2.log(input="c", output="out", scores=dict(score0=1))
        e2.log(input="c", output="out", scores=dict(score0=1))
        e2.summarize()

        self.verify_brainstore_summary(e1)
        self.verify_brainstore_summary(e2)

        # The query should average the scores across inputs, so the scores should be the same
        scores = self.get_experiment_comparison(e1.id, e2.id)["scores"]["score0"]
        self.assertEqual(scores["name"], "score0")
        self.assertAlmostEqual(scores["score"], 0.5)
        self.assertAlmostEqual(scores["diff"], 0.15)
        self.assertEqual(scores["improvements"], 1)  # Improvements in terms of # of inputs, not unique rows
        self.assertEqual(scores["regressions"], 0)

    def test_nonoverlapping_scores(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")
        e1.log(input="a", output="out", scores=dict(score0=0.7))
        e1.log(input="b", output="out", scores=dict(score0=0.2))
        e1.summarize()
        e2.log(input="a", output="out", scores=dict(score1=0.2))
        e2.log(input="b", output="out", scores=dict(score1=0.4))

        # The query should average the scores across inputs, so the scores should be the same
        scores = self.get_experiment_comparison(e1.id, e2.id)
        self.assertEqual(len(scores), 2)  # Only one score, score0
        self.assertEqual(scores["scores"]["score0"]["name"], "score0")
        self.assertEqual(scores["metrics"]["duration"]["name"], "duration")

    def test_subspan_average(self):
        e1 = braintrust.init(project="p")
        e2 = braintrust.init(project="p")

        with e1.start_span() as root_span:
            root_span.log(input="a", output="out", scores=dict(foo=0.0))
        with e2.start_span() as root_span:
            root_span.log(input="a", output="b", scores={})

            for i in range(4):
                with root_span.start_span() as child_span:
                    child_span.log(input="a", output="b", scores=dict(foo=i / 4))

        e1.summarize()
        e2.summarize()

        self.verify_brainstore_summary(e1)
        self.verify_brainstore_summary(e2)

        scores = self.get_experiment_comparison(e2.id, e1.id)
        self.assertEqual(scores["scores"]["foo"]["score"], 0.375)
        self.assertEqual(scores["scores"]["foo"]["diff"], 0.375)

    def test_cached(self):
        # First test a single span
        for cached in [None, 0, 1]:
            experiment = braintrust.init(project="p", metadata={"cached": cached})
            with experiment.start_span() as root_span:
                with root_span.start_span(type="llm") as llm_span:
                    metrics = {"prompt_tokens": 500, "completion_tokens": 250}
                    if cached is not None:
                        metrics["cached"] = cached
                    llm_span.log(input="a", output="out", metrics=metrics, metadata=dict(model="gpt-3.5-turbo"))
                root_span.log(input="a", output="out", scores=dict(foo=0.0))
            summary = experiment.summarize()

            self.assertIsNotNone(summary.scores.get("foo"))
            duration_val = summary.metrics.get("duration")
            self.assertIsNotNone(duration_val)

            for metric in ["estimated_cost"]:
                metric_val = summary.metrics.get(metric)
                if cached is None or cached == 0:
                    self.assertIsNotNone(metric_val)
                    self.assertIsInstance(metric_val.metric, (int, float))
                else:
                    self.assertEqual(metric_val.metric, 0)

            self.verify_brainstore_summary(experiment)

        # Next try making two spans, where one is cached or not cached, and the other is null
        # The null span should not affect the metrics
        for cached in [0, 1]:
            experiment = braintrust.init(project="p", metadata={"cached": cached})

            with experiment.start_span() as root_span:
                with root_span.start_span(type="llm") as llm_span:
                    metrics = {"prompt_tokens": 500, "completion_tokens": 250}
                    if cached is not None:
                        metrics["cached"] = cached
                    llm_span.log(input="a", output="out", metrics=metrics, metadata=dict(model="gpt-3.5-turbo"))
                with root_span.start_span() as null_span:
                    null_span.log(input="a", output="out")
                root_span.log(input="a", output="out", scores=dict(foo=0.0))
            summary = experiment.summarize()

            self.assertIsNotNone(summary.scores.get("foo"))
            duration_val = summary.metrics.get("duration")
            self.assertIsNotNone(duration_val)

            for metric in ["estimated_cost"]:
                metric_val = summary.metrics.get(metric)
                if cached is None or cached == 0:
                    self.assertIsNotNone(metric_val)
                    self.assertIsInstance(metric_val.metric, (int, float))
                else:
                    self.assertEqual(metric_val.metric, 0)

            self.verify_brainstore_summary(experiment)

        # Finally, try making two spans, where one is cached and the other is not
        # The duration metric should be present because there is at least one non-cached span
        experiment = braintrust.init(project="p", metadata={"cached": "both"})
        with experiment.start_span() as root_span:
            with root_span.start_span(type="llm") as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 0, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-3.5-turbo"),
                )
            with root_span.start_span(type="llm") as llm_span:
                llm_span.log(
                    input="a",
                    output="out",
                    metrics={"cached": 1, "prompt_tokens": 500, "completion_tokens": 250},
                    metadata=dict(model="gpt-3.5-turbo"),
                )
            root_span.log(input="a", output="out", scores=dict(foo=0.0))
        summary = experiment.summarize()
        for metric in ["duration", "estimated_cost"]:
            metric_val = summary.metrics.get(metric)
            self.assertIsNotNone(metric_val)

        self.verify_brainstore_summary(experiment)
